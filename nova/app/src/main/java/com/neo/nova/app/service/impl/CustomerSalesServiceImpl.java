package com.neo.nova.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.neo.api.MultiResponse;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.helper.ChartHelper;
import com.neo.nova.app.service.CustomerSalesService;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.service.PerformanceService;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.constants.DataConstants;
import com.neo.nova.domain.dto.*;
import com.neo.nova.domain.entity.*;
import com.neo.nova.domain.enums.*;
import com.neo.nova.domain.excelExport.StatisticsExport;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_month_infoRepository;
import com.neo.user.client.tenant.api.DepartmentService;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.RoundingMode;

import com.neo.nova.domain.enums.TargetDataTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class CustomerSalesServiceImpl implements CustomerSalesService {
    @Autowired
    private st_trd_customer_produce_day_infoRepository st_trd_customer_produce_day_infoRepository;
    @Autowired
    private st_trd_customer_produce_month_infoRepository st_trd_customer_produce_month_infoRepository;
    @Resource
    private MetricService metricService;
    @Resource
    private PerformanceService performanceService;
    @Resource
    private CustomerService customerService;
    @Resource
    private DepartmentService departmentService;

    /**
     * 销售额统计
     *
     * @param dataFormQueryVO
     * @return
     */
    @Override
    public DataFormVO statistics(DataFormQueryVO dataFormQueryVO) {

        // 表格查询 - 当前数据
        IPage<Map<String, Object>> tableResultMaps = batchActualValue(dataFormQueryVO);
        List<Map<String, Object>> tableRecords = tableResultMaps.getRecords();

        // 对计算字段进行排序（如果需要）
        sortCalculatedFields(tableRecords, dataFormQueryVO);

        // 组装表格
        Table table = new Table();
        // 添加查询条件列
        dataFormQueryVO.getQueryOptions().keySet().forEach(table::addMetricCodeColumn);

        // 添加新的数据列（根据targetDataTypes参数决定）
        List<String> dataColumns = addEnhancedColumns(table, dataFormQueryVO.getTargetDataTypes());

        // 填充表格数据
        tableRecords.forEach(map -> table.addRecord(map, dataColumns));

        // 设置分页信息
        table.setPageSize(tableResultMaps.getSize());
        table.setTotalPage(tableResultMaps.getPages());
        table.setCurrentPage(tableResultMaps.getCurrent());
        table.setTotal(tableResultMaps.getTotal());

        // 组装返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);

        return dataFormVO;
    }


    private <T> void statistics_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, List<String> selectDataColumns, int queryType) {
        //groupBy
        if (queryType != 1) {
            dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).forEach(wrapper::groupBy);
        }
        //查询条件赋值
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperValueSearch(wrapper, key, value));
        //添加条件字段
        List<String> selectColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());
        //处理排序 - 支持计算字段排序
        handleSortingForCalculatedFields(wrapper, dataFormQueryVO, selectColumns, selectDataColumns);
        //添加查询字段
        selectColumns.addAll(selectDataColumns);

        wrapper.select(selectColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
                .ne("produceId", -1)  //过滤无效数据
        ;

        if (!selectColumns.contains("produceName") && !selectColumns.contains("produceTypeId")) {
            //查询条件中不带produceName和produceTypeId时 ，查询全店合计
            wrapper.eq("produceId", 0);
        } else {
            //查询商品时，过滤全店
            wrapper.ne("produceId", 0);
        }
    }


    /**
     * 销售趋势
     *
     * @param dataFormQueryVO
     * @return
     */
    @Override
    public DataFormVO trends(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return null;
        }
        //对时间进行合法校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        //table查询
        IPage<Map<String, Object>> tableResultMaps;
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO);
            tableResultMaps = st_trd_customer_produce_day_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO);
            tableResultMaps = st_trd_customer_produce_month_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        }

        // 批量映射ID到中文名称
        List<Map<String, Object>> tableRecords = tableResultMaps.getRecords();
        batchMapIdToNames(tableRecords, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        //组装table
        Table table = new Table();
        table.addToFirstColumn(Column.builder().key("visit_date").title("日期").isSort(true).build());
        dataFormQueryVO.getQueryOptions().keySet().forEach(table::addMetricCodeColumn);
        table.addToFirstColumn(Column.builder().key("amount").title("销售额").isSort(true).build());
        tableRecords.forEach(map -> table.addRecord(map, Lists.newArrayList("amount")));

        table.setPageSize(tableResultMaps.getSize());
        table.setTotalPage(tableResultMaps.getPages());
        table.setCurrentPage(tableResultMaps.getCurrent());
        table.setTotal(tableResultMaps.getTotal());


        //查询chart
        List<Map<String, Object>> chartResultMaps;
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO);
            chartResultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO);
            chartResultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射chart数据的ID到中文名称
        batchMapIdToNames(chartResultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        //组装chart
        Chart<String, String> chart = ChartHelper.buildMultiDimensionChart(chartResultMaps,
                dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::tableNameMapping).toList(), "visit_date", "amount");


        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);
        dataFormVO.setChart(chart);
        return dataFormVO;

    }


    private static void timeConditionCheck(TimeCondition timeCondition) {
        if (timeCondition == null) {
            throw new BizCustomException(104, "时间条件不能为空");
        }
        //对periodType进行合法校验
        Integer periodType = timeCondition.getPeriodType();
        if (Arrays.stream(PeriodTypeEnum.values()).map(PeriodTypeEnum::getCode).noneMatch(code -> code.equals(periodType))) {
            throw new BizCustomException(100, "无效的周期类型: " + periodType);
        }
        //对时间进行校验
        String startDate = timeCondition.getStartDate();
        String endDate = timeCondition.getEndDate();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            // 校验时间格式是否为 yyyy-MM-dd
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM-dd: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM-dd: " + endDate);
            }
        } else {
            // 校验时间格式是否为 yyyy-MM
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM: " + endDate);
            }
        }

        // 校验开始时间不能晚于结束时间
        if (startDate.compareTo(endDate) > 0) {
            throw new BizCustomException(103, "开始时间不能晚于结束时间");
        }

    }

    private <T> void _buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO) {
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperAssembly(wrapper, key, value));
        List<String> selectColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());

        //先处理sort
        wrapper.orderBy(StringUtils.isBlank(dataFormQueryVO.getSortBy()), false, "visit_date", selectColumns);
        wrapper.orderBy(StringUtils.isNotBlank(dataFormQueryVO.getSortBy()), !"desc".equals(dataFormQueryVO.getSort()), dataFormQueryVO.getSortBy());

        selectColumns.add("visit_date");
        selectColumns.add("sum(salesAmount) as amount");
        wrapper.select(selectColumns)
                .groupBy("visit_date")
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
                .ne("produceId", -1)  //过滤无效数据
        ;

        if (!selectColumns.contains("produceName") && !selectColumns.contains("produceTypeId")) {
            //查询条件中不带produceName和produceTypeId时 ，查询全店合计
            wrapper.eq("produceId", 0);
        } else {
            //查询商品时，过滤全店
            wrapper.ne("produceId", 0);
        }
    }


    private static <T> void wrapperAssembly(QueryWrapper<T> wrapper, String metricCode, List<String> values) {
        String selectColumn = MetricCodeEnum.metricCodeMapping(metricCode);
        wrapper.groupBy(selectColumn);
        valueSearchSet(wrapper, metricCode, selectColumn, values);
        //查询复杂指标时，去除空项
        if (MetricCodeEnum.isComplexMetric(metricCode)) {
            wrapper.ne(selectColumn, "").isNotNull(selectColumn);
        }
    }

    private static <T> void wrapperValueSearch(QueryWrapper<T> wrapper, String metricCode, List<String> values) {
        String selectColumn = MetricCodeEnum.metricCodeMapping(metricCode);
        valueSearchSet(wrapper, metricCode, selectColumn, values);
        //查询复杂指标时，去除空项
        if (MetricCodeEnum.isComplexMetric(metricCode)) {
            wrapper.ne(selectColumn, "").isNotNull(selectColumn);
        }
    }

    private static <T> void valueSearchSet(QueryWrapper<T> wrapper, String metricCode, String selectColumn, List<String> values) {
        if (CollectionUtil.isEmpty(values)) {
            return;
        }
        //简写搜索特判
        if (MetricCodeEnum.CUSTOMER_NAME.getCode().equals(metricCode)) {
            wrapper.nested(wq ->
                    wq.like(selectColumn, values.get(0))
                            .or()
                            .likeLeft("customerMnemoCode", values.get(0))
            );
            return;
        }
        if (MetricCodeEnum.PRODUCT_NAME.getCode().equals(metricCode)
                || MetricCodeEnum.CUSTOMER_OWNER.getCode().equals(metricCode)) {
            wrapper.like(selectColumn, values.get(0));
            return;
        }

        // 产品来源特殊处理：自产=0，外购=1
        if (MetricCodeEnum.PRODUCT_SOURCE.getCode().equals(metricCode)) {
            List<Integer> oemFlags = values.stream().map(value -> {
                if ("SELF".equals(value)) {
                    return 0; // 自产
                } else if ("EXTERNAL".equals(value)) {
                    return 1; // 外购
                }
                return Integer.parseInt(value);
            }).toList();
            wrapper.in(selectColumn, oemFlags);
            return;
        }

        wrapper.in(selectColumn, values);
    }


    /**
     * 饼图统计
     *
     * @param currentTime 格式：YYYYMM，例如：202501
     * @return 饼图数据
     */
    @Override
    public PieChartVO pieChart(Long tenantId, String currentTime) {
        try {

            LocalDateTime localDateTime = LocalDateTimeUtil.parse(currentTime, "yyyy-MM");

            String yearStartMonth = localDateTime.getYear() + "-01";
            String yearEndMonth = localDateTime.getYear() + "-12";

            // 获取目标值
            BigDecimal yearTargetValue = performanceService.queryRootTargets(tenantId, new TimeCondition(PeriodTypeEnum.YEAR.getCode(), yearStartMonth, yearEndMonth));
            BigDecimal monthTargetValue = performanceService.queryRootTargets(tenantId, new TimeCondition(PeriodTypeEnum.MONTH.getCode(), currentTime, currentTime));

            // 获取实际销售额

            BigDecimal yearActualValue = _queryAllCompany(tenantId, new TimeCondition(PeriodTypeEnum.YEAR.getCode(), yearStartMonth, yearEndMonth));
            BigDecimal monthActualValue = _queryAllCompany(tenantId, new TimeCondition(PeriodTypeEnum.MONTH.getCode(), currentTime, currentTime));

            // 计算时间进度
            TimeProgress timeProgress = calculateTimeProgress(localDateTime);

            // 构建返回结果
            PieChartVO pieChartVO = PieChartVO.builder()
                    .yearTargetValue(yearTargetValue)
                    .monthTargetValue(monthTargetValue)
                    .yearActualValue(yearActualValue)
                    .monthActualValue(monthActualValue)
                    .yearTimePercent(timeProgress.getYearTimePercent())
                    .monthTimePercent(timeProgress.getMonthTimePercent())
                    .build();

            // 计算实现率、状态和剩余目标
            pieChartVO.calculatePercents();
            pieChartVO.calculateStatus();
            pieChartVO.calculateRemainingTargets();

            return pieChartVO;

        } catch (BizCustomException e) {
            throw e;
        } catch (Exception e) {
            throw new BizCustomException(500, "饼图数据查询失败：" + e.getMessage());
        }
    }

    @Override
    public Map<Long, PerformanceProgressDTO> getProgress(Long tenantId, List<Long> userIds, TimeCondition timeCondition) {
        if (timeCondition == null || timeCondition.getStartDate() == null) {
            return null;
        }

        Map<Long, PerformanceProgressDTO> result = Maps.newHashMap();
        try {
            // 2. 查询目标值
            Map<Long, BigDecimal> targetValues = performanceService.queryUserTargets(tenantId, userIds, timeCondition);

            // 3. 批量查询实际进度值
            Map<Long, BigDecimal> progressValueMap = queryActualValueForUser(tenantId, userIds, timeCondition);

            // 4. 计算完成百分比
            for (Long userId : userIds) {
                BigDecimal targetValue = targetValues.getOrDefault(userId, BigDecimal.ZERO);
                BigDecimal progressValue = progressValueMap.getOrDefault(userId, BigDecimal.ZERO);
                BigDecimal percent = BigDecimal.ZERO;
                if (targetValue.compareTo(BigDecimal.ZERO) > 0) {
                    percent = progressValue.divide(targetValue, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"))
                            .setScale(2, RoundingMode.HALF_UP);
                }
                result.put(userId, PerformanceProgressDTO.builder()
                        .timeCondition(timeCondition)
                        .progressValue(progressValue)
                        .target(targetValue)
                        .percent(percent)
                        .build());
            }
        } catch (Exception e) {
            throw new BizCustomException(500, "查询进度失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 计算时间进度
     */
    private TimeProgress calculateTimeProgress(LocalDateTime localDateTime) {
        LocalDateTime now = LocalDateTimeUtil.now();

        // 未来
        if (now.getYear() > localDateTime.getYear() || now.getMonthValue() > localDateTime.getMonthValue()) {
            // 未来时间，进度为0
            return new TimeProgress(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        //过去
        if (now.getYear() < localDateTime.getYear() || now.getMonthValue() < localDateTime.getMonthValue()) {
            return new TimeProgress(BigDecimal.ONE, BigDecimal.ONE);
        }

        // 年度时间进度
        BigDecimal yearTimePercent = new BigDecimal(now.getMonthValue()).divide(new BigDecimal("12"), 4, RoundingMode.HALF_UP);
        // 当前月份，按天计算
        BigDecimal monthTimePercent = new BigDecimal(now.getDayOfMonth()).divide(new BigDecimal(now.getMonth().length(now.getYear() % 4 == 0)), 4, RoundingMode.HALF_UP);

        return new TimeProgress(yearTimePercent, monthTimePercent);
    }

    /**
     * 时间进度内部类
     */
    @Getter
    private static class TimeProgress {
        private final BigDecimal yearTimePercent;
        private final BigDecimal monthTimePercent;

        public TimeProgress(BigDecimal yearTimePercent, BigDecimal monthTimePercent) {
            this.yearTimePercent = yearTimePercent;
            this.monthTimePercent = monthTimePercent;
        }
    }


    @Override
    public List<StatisticsExport> StatisticsExport(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            statisticsExport_buildWrapper(wrapper, dataFormQueryVO);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            statisticsExport_buildWrapper(wrapper, dataFormQueryVO);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        // 转换为StatisticsExport对象列表
        List<StatisticsExport> exportList = new ArrayList<>();
        for (int i = 0; i < resultMaps.size(); i++) {
            Map<String, Object> map = resultMaps.get(i);
            StatisticsExport export = new StatisticsExport();

            // 设置序号
            export.setId((long) (i + 1));

            // 设置基本字段
            export.setProduceName(getStringValue(map, "produceName"));
            export.setProduceCode(getStringValue(map, "produceCode"));
            export.setCustomerName(getStringValue(map, "customerName"));
            export.setCustomerCode(getStringValue(map, "customerCode"));
            export.setCustomerLevel(getIntegerValue(map, "customerLevel"));
            export.setSalesName(getStringValue(map, "salesName"));
            export.setSalesCode(getStringValue(map, "salesCode"));
            export.setChannelId(getStringValue(map, "channelId"));
            // 暂时使用customerTypeId作为customerType，后续可以根据需要添加类型名称映射
            export.setCustomerType(getStringValue(map, "customerTypeId"));
            export.setAmount(getLongValue(map, "amount"));
            export.setVisitDate(getStringValue(map, "visit_date"));

            // 设置中文名称字段
            export.setChannelName(getStringValue(map, "channelName"));
            export.setSupermarketAreaName(getStringValue(map, "supermarketAreaName"));
            export.setAdminRegionName(getStringValue(map, "adminRegionName"));
            export.setCustomerLevelName(getStringValue(map, "customerLevelName"));
            export.setProduceTypeName(getStringValue(map, "produceTypeName"));

            exportList.add(export);
        }

        return exportList;
    }

    /**
     * 为Excel导出构建查询条件（不分页，获取所有数据）
     */
    private <T> void statisticsExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO) {
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperAssembly(wrapper, key, value));

        // 选择需要的字段，包括分组字段和聚合字段
        List<String> selectColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());
        selectColumns.add("produceName");
        selectColumns.add("produceCode");
        selectColumns.add("customerName");
        selectColumns.add("customerCode");
        selectColumns.add("customerLevel");
        selectColumns.add("customerTypeId");
        selectColumns.add("salesName");
        selectColumns.add("salesCode");
        selectColumns.add("channelId");
        selectColumns.add("sum(salesAmount) as amount");
        selectColumns.add("visit_date");

        // 构建GROUP BY子句，包含所有非聚合字段
        List<String> groupByColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());
        groupByColumns.add("produceName");
        groupByColumns.add("produceCode");
        groupByColumns.add("customerName");
        groupByColumns.add("customerCode");
        groupByColumns.add("customerLevel");
        groupByColumns.add("customerTypeId");
        groupByColumns.add("salesName");
        groupByColumns.add("salesCode");
        groupByColumns.add("channelId");
        groupByColumns.add("visit_date");

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate());
    }

    /**
     * 安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Override
    public List<StatisticsExport> trendsExport(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            trendsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            trendsExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        // 转换为StatisticsExport对象列表
        List<StatisticsExport> exportList = new ArrayList<>();
        for (int i = 0; i < resultMaps.size(); i++) {
            Map<String, Object> map = resultMaps.get(i);
            StatisticsExport export = new StatisticsExport();

            // 设置序号
            export.setId((long) (i + 1));

            // 设置固定字段：时间和销售额
            export.setVisitDate(getStringValue(map, "visit_date"));
            export.setAmount(getLongValue(map, "amount"));

            // 动态设置条件列的值
            setDynamicColumns(export, map, columnMapping);

            // 设置中文名称字段
            export.setChannelName(getStringValue(map, "channelName"));
            export.setSupermarketAreaName(getStringValue(map, "supermarketAreaName"));
            export.setAdminRegionName(getStringValue(map, "adminRegionName"));
            export.setCustomerLevelName(getStringValue(map, "customerLevelName"));
            export.setProduceTypeName(getStringValue(map, "produceTypeName"));

            exportList.add(export);
        }

        return exportList;
    }

    /**
     * 为趋势导出构建查询条件（动态分组，不过滤数据）
     */
    private <T> void trendsExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        // 处理查询条件，但不进行数据过滤，只用于分组
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> {
            String selectColumn = MetricCodeEnum.metricCodeMapping(key);
            if (selectColumn != null) {
                columnMapping.put(selectColumn, MetricCodeEnum.getNameByCode(key));
            }
        });

        // 构建SELECT子句：固定列 + 动态条件列 + 聚合列
        List<String> selectColumns = Lists.newArrayList();
        selectColumns.add("visit_date");
        selectColumns.addAll(columnMapping.keySet()); // 添加动态条件列
        selectColumns.add("sum(salesAmount) as amount");

        // 构建GROUP BY子句：时间 + 动态条件列
        List<String> groupByColumns = Lists.newArrayList();
        groupByColumns.add("visit_date");
        groupByColumns.addAll(columnMapping.keySet()); // 按条件列分组

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .orderBy(true, true, "visit_date"); // 按时间排序
    }

    /**
     * 动态设置条件列的值
     */
    private void setDynamicColumns(StatisticsExport export, Map<String, Object> map, Map<String, String> columnMapping) {
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            String columnName = entry.getKey();
            String columnTitle = entry.getValue();
            Object value = map.get(columnName);

            // 根据列名设置对应的字段值
            switch (columnName) {
                case "produceName":
                    export.setProduceName(getStringValue(map, columnName));
                    break;
                case "produceCode":
                    export.setProduceCode(getStringValue(map, columnName));
                    break;
                case "customerName":
                    export.setCustomerName(getStringValue(map, columnName));
                    break;
                case "customerCode":
                    export.setCustomerCode(getStringValue(map, columnName));
                    break;
                case "customerLevel":
                    export.setCustomerLevel(getIntegerValue(map, columnName));
                    break;
                case "customerTypeId":
                    export.setCustomerType(getStringValue(map, columnName));
                    break;
                case "salesName":
                    export.setSalesName(getStringValue(map, columnName));
                    break;
                case "salesCode":
                    export.setSalesCode(getStringValue(map, columnName));
                    break;
                case "channelId":
                    export.setChannelId(getStringValue(map, columnName));
                    break;
                default:
                    // 对于其他字段，可以根据需要扩展
                    break;
            }
        }
    }

    @Override
    public List<Map<String, Object>> getDynamicExportData(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        // 查询数据（不分页，获取所有数据用于导出）
        List<Map<String, Object>> resultMaps;
        Map<String, String> columnMapping = Maps.newLinkedHashMap();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            dynamicExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            dynamicExport_buildWrapper(wrapper, dataFormQueryVO, columnMapping);
            resultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        // 批量映射ID到中文名称
        batchMapIdToNames(resultMaps, dataFormQueryVO.getTenantId()); // 使用默认租户ID，可根据实际情况调整

        return resultMaps;
    }

    /**
     * 为动态导出构建查询条件（只分组，不过滤）
     */
    private <T> void dynamicExport_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, Map<String, String> columnMapping) {
        // 处理查询条件，但不进行数据过滤，只用于分组
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> {
            String selectColumn = MetricCodeEnum.metricCodeMapping(key);
            if (selectColumn != null) {
                columnMapping.put(selectColumn, MetricCodeEnum.getNameByCode(key));
            }
        });

        // 构建SELECT子句：固定列 + 动态条件列 + 聚合列
        List<String> selectColumns = Lists.newArrayList();
        selectColumns.add("visit_date");
        selectColumns.addAll(columnMapping.keySet()); // 添加动态条件列
        selectColumns.add("sum(salesAmount) as amount");

        // 构建GROUP BY子句：时间 + 动态条件列
        List<String> groupByColumns = Lists.newArrayList();
        groupByColumns.add("visit_date");
        groupByColumns.addAll(columnMapping.keySet()); // 按条件列分组

        wrapper.select(selectColumns)
                .groupBy(groupByColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .orderBy(true, true, "visit_date"); // 按时间排序
    }

    /**
     * 批量映射ID到中文名称
     */
    private void batchMapIdToNames(List<Map<String, Object>> resultMaps, Long tenantId) {
        if (resultMaps == null || resultMaps.isEmpty()) {
            return;
        }

        // 收集所有需要映射的ID
        Set<Long> customerSalesRegionMap = new HashSet<>();
        Set<Long> adminRegionIds = new HashSet<>();
        Set<Long> produceTypeIds = new HashSet<>();
        Set<Long> customerTypeIds = new HashSet<>();
        Set<Long> salesIds = new HashSet<>();
        Set<Long> departIds = new HashSet<>();

        for (Map<String, Object> map : resultMaps) {
            collectId(map, "salesRegionId", customerSalesRegionMap);
            collectId(map, "adminRegionId", adminRegionIds);
            collectId(map, "produceTypeId", produceTypeIds);
            collectId(map, "customerTypeId", customerTypeIds);
            collectId(map, "salesId", salesIds);
            collectId(map, "departId", departIds);
        }

        // 批量查询名称映射
        Map<Long, String> customerSalesRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), customerSalesRegionMap);
        Map<Long, String> adminRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_ADMIN_REGION.getCode(), adminRegionIds);
        Map<Long, String> produceTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.PRODUCT_TYPE.getCode(), produceTypeIds);
        Map<Long, String> customerTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_TYPE.getCode(), customerTypeIds);
        Map<Long, String> salesNameMap = metricService.queryNames(tenantId, MetricCodeEnum.USER.getCode(), salesIds);
        Map<Long, String> departNameMap = metricService.queryNames(tenantId, MetricCodeEnum.DEPARTMENT.getCode(), departIds);

        // 将名称映射回原始数据
        for (Map<String, Object> map : resultMaps) {
            Optional.ofNullable(map.get("channelId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("channelName", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("supermarketAreaId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("supermarketAreaName", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("customerLevel")).map(String::valueOf).map(Long::valueOf).map(CustomerLevelEnum::getById)
                    .ifPresent(customerLevelEnum -> map.put("customerLevelName", customerLevelEnum.getDesc()));
            mapIdToName(map, "salesRegionId", "salesRegionName", customerSalesRegionNameMap);
            mapIdToName(map, "adminRegionId", "adminRegionName", adminRegionNameMap);
            mapIdToName(map, "produceTypeId", "produceTypeName", produceTypeNameMap);
            mapIdToName(map, "customerTypeId", "customerTypeName", customerTypeNameMap);
            mapIdToName(map, "salesId", "salesName", salesNameMap);
            mapIdToName(map, "departId", "departName", departNameMap);

        }
    }

    /**
     * 从Map中收集指定字段的ID
     */
    private void collectId(Map<String, Object> map, String fieldName, Set<Long> idSet) {
        Object value = map.get(fieldName);
        if (value != null) {
            try {
                Long id = Long.valueOf(value.toString());
                idSet.add(id);
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    /**
     * 将ID映射为名称并添加到Map中
     */
    private void mapIdToName(Map<String, Object> map, String idFieldName, String nameFieldName, Map<Long, String> nameMap) {
        Object idValue = map.get(idFieldName);
        if (idValue != null) {
            try {
                Long id = Long.valueOf(idValue.toString());
                String name = nameMap.get(id);
                if (name != null) {
                    map.put(nameFieldName, name);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    @Override
    public DataFormVO salesReport(SalesReportQueryVO queryVO) {
        if (queryVO == null || queryVO.getChannelMetricCodeId() == null ||
                queryVO.getTimeCondition() == null || queryVO.getTimeCondition().getStartDate() == null) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

//        List<CustomerDTO> customerInfoList = customerService.searchDTO(CustomerQueryVO.builder().tenantId(queryVO.getTenantId())
//                .status(Lists.newArrayList(CustomerStatusEnum.NORMAL.getCode()))
//                .channelId(queryVO.getChannelMetricCodeId()).build());
//        if (customerInfoList == null || customerInfoList.isEmpty()) {
//            return null;
//        }
//        List<Long> customerIds = customerInfoList.stream().map(CustomerDTO::getId).toList();
//        Map<Long, CustomerDTO> customerInfoMap = customerInfoList.stream()
//                .collect(Collectors.toMap(CustomerDTO::getId,
//                        Function.identity(),
//                        (customerInfo, customerInfo2) -> customerInfo
//                ));


        // 构建查询条件
        QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
        wrapper.select(
                        "customerId",
                        "sum(salesAmount) as salesAmount",
                        "sum(costAmount) as costAmount",
                        "(sum(salesAmount) - sum(costAmount)) as grossProfitAmount"
                )
                .eq("visit_date", queryVO.getTimeCondition().getStartDate())
                .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", queryVO.getTenantId())
                .eq("channelId", queryVO.getChannelMetricCodeId())
                .eq("isDeleted", 0)
                .groupBy("customerId");

        // 分页查询
        List<Map<String, Object>> records = st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(wrapper);

        Set<Long> customerIds = records.stream().filter(Objects::nonNull)
                .map(record -> record.get("customerId"))
                .filter(Objects::nonNull).map(Object::toString).map(Long::parseLong)
                .collect(Collectors.toSet());
        List<CustomerDTO> customerInfoList = customerService.searchDTO(CustomerQueryVO.builder().tenantId(queryVO.getTenantId())
                .ids(customerIds).build());
        Map<Long, CustomerDTO> customerInfoMap = customerInfoList.stream()
                .collect(Collectors.toMap(CustomerDTO::getId,
                        Function.identity(),
                        (customerInfo, customerInfo2) -> customerInfo
                ));

        // 构建表格
        Table table = new Table();
        table.setTitle("销售报表");

        buildSalesReportTitle(table, queryVO.getChannelMetricCodeId());


        Map<Long, Map<String, Object>> resultMap = records.stream()
                .filter(record -> record != null && record.containsKey("customerId"))
                .collect(Collectors.toMap(
                        record -> Long.valueOf(record.get("customerId").toString()),
                        Function.identity(),
                        (record1, record2) -> record1
                ));

        for (Long customerId : customerIds) {
            table.addRow(Maps.newHashMap());

            Map<String, Object> record = resultMap.getOrDefault(customerId, Maps.newHashMap());
            CustomerDTO customerInfo = customerInfoMap.get(customerId);
            table.addToLastRow("adminRegionId", Cell.builder().value(customerInfo.getAdminRegionName()).build());
            table.addToLastRow("salesId", Cell.builder().value(customerInfo.getSalesName()).build());
            table.addToLastRow("customerName", Cell.builder().value(customerInfo.getName()).build());
            //销售收入
            BigDecimal salesAmount = new BigDecimal(record.getOrDefault("salesAmount", "0").toString())
                    .setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("salesAmount", Cell.builder().value(salesAmount).build());
            //采购价格
            BigDecimal costAmount = new BigDecimal(record.getOrDefault("costAmount", "0").toString())
                    .setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("costAmount", Cell.builder().value(costAmount).build());
            //毛利
            BigDecimal grossProfitAmount = new BigDecimal(record.getOrDefault("grossProfitAmount", "0").toString())
                    .setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("grossProfitAmount", Cell.builder().value(grossProfitAmount).build());
            //毛利率
            BigDecimal grossProfitAmountRate = salesAmount.compareTo(BigDecimal.ZERO) <= 0 ?
                    BigDecimal.ZERO : grossProfitAmount.divide(salesAmount, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            Cell grossProfitAmountRateCell = Cell.builder().value(grossProfitAmountRate).build();
            if (MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode().equals(queryVO.getChannelMetricCodeId())) {
                if (grossProfitAmountRate.compareTo(new BigDecimal("30")) < 0) {
                    grossProfitAmountRateCell.setColor("F43E3E").setBold();
                }
            } else {
                if (grossProfitAmountRate.compareTo(new BigDecimal("15")) < 0) {
                    grossProfitAmountRateCell.setColor("F43E3E").setBold();
                }
            }
            table.addToLastRow("grossProfitAmountRate", grossProfitAmountRateCell);
            //销售扣点
            BigDecimal marketDeducePoint = BigDecimal.ZERO;
            //固定耗材费用
            BigDecimal fixedConsumablesCost = BigDecimal.ZERO;
            //导购管理费
            BigDecimal guideManagementCost = BigDecimal.ZERO;
            //电费
            BigDecimal electricityCost = BigDecimal.ZERO;
            //配送费
            BigDecimal deliveryCost = BigDecimal.ZERO;
            //促销费
            BigDecimal promotionCost = BigDecimal.ZERO;
            //推广费
            BigDecimal marketingCost = BigDecimal.ZERO;
            //进场费
            BigDecimal slottingFee = BigDecimal.ZERO;
            //其他费用
            BigDecimal otherCosts = BigDecimal.ZERO;
            //小计
            BigDecimal supermarketCostSubtotal = BigDecimal.ZERO;

            if (MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode().equals(queryVO.getChannelMetricCodeId())) {
                //销售扣点
                marketDeducePoint = salesAmount.multiply(BigDecimal.valueOf(0.13)).setScale(2, RoundingMode.HALF_UP);

                //小计
                supermarketCostSubtotal = marketDeducePoint.add(fixedConsumablesCost).add(guideManagementCost)
                        .add(electricityCost).add(deliveryCost).add(promotionCost).add(marketingCost)
                        .add(slottingFee).add(otherCosts);
            }
            table.addToLastRow("marketDeducePoint", Cell.builder().value(marketDeducePoint).build());
            table.addToLastRow("supermarketCostSubtotal", Cell.builder().value(supermarketCostSubtotal).build());

            //员工社保
            BigDecimal employeeCost = BigDecimal.ZERO;

            //出清损耗
            BigDecimal clearanceLoss = BigDecimal.ZERO;
            //损耗
            BigDecimal wastageLoss = BigDecimal.ZERO;
            //小计
            BigDecimal lossSubtotal = clearanceLoss.add(wastageLoss);

            //冰柜折旧
            BigDecimal freezerCost = BigDecimal.ZERO;
            //基础物料
            BigDecimal basicSuppliesCost = BigDecimal.ZERO;
            //试吃物料
            BigDecimal productSamplingCost = BigDecimal.ZERO;
            //自购物料
            BigDecimal personalBuyCost = BigDecimal.ZERO;
            //小计
            BigDecimal supermarketSetupSubtotal = freezerCost.add(basicSuppliesCost).add(productSamplingCost).add(personalBuyCost);

            //活动推广费
            BigDecimal campaignPromotionCost = BigDecimal.ZERO;

            //销售费用合计
            BigDecimal totalSalesCost = supermarketCostSubtotal.add(employeeCost).add(lossSubtotal)
                    .add(supermarketSetupSubtotal).add(campaignPromotionCost);
            table.addToLastRow("totalSalesCost", Cell.builder().value(totalSalesCost).build());

            //直接费用
            BigDecimal directFulfillmentCost = BigDecimal.ZERO;
            //分摊费用
            BigDecimal allocatedFulfillmentCost = BigDecimal.ZERO;
            //小计
            BigDecimal fulfillmentCostsSubtotal = directFulfillmentCost.add(allocatedFulfillmentCost);

            //运营毛利
            BigDecimal operatingGrossProfit = grossProfitAmount.subtract(totalSalesCost).subtract(fulfillmentCostsSubtotal)
                    .setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("operatingGrossProfit", Cell.builder().value(operatingGrossProfit).build());

            //运营毛利率
            BigDecimal operatingGrossProfitMargin = salesAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO :
                    operatingGrossProfit.divide(salesAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("operatingGrossProfitMargin", Cell.builder().value(operatingGrossProfitMargin).build());

        }

        // 设置分页信息
        table.setPageSize((long) records.size());
        table.setTotalPage(1L);
        table.setCurrentPage(1L);
        table.setTotal((long) records.size());

        // 构建返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);

        return dataFormVO;
    }

    private void buildSalesReportTitle(Table table, String channelCodeId) {
        // 添加表头
        table
//                .addToFirstColumn(Column.builder().key("customerTypeId").title("客户分类").isSort(true).isFixed(true).build())
                .addToFirstColumn(Column.builder().key("adminRegionId").title("区域").isSort(true).isFixed(true).build())
                .addToFirstColumn(Column.builder().key("salesId").title("负责人").isSort(true).isFixed(true).build())
                .addToFirstColumn(Column.builder().key("customerName")
                        .title("客户名称")
                        .isSort(true).isFixed(true).build());

        //销售毛利
        Column amount = Column.builder().key("salesAmountGlobal").title("销售毛利").build()
                .addChild(Column.builder().key("salesAmount").title("销售收入").isSort(true).build())
                .addChild(Column.builder().key("costAmount").title("采购价格").isSort(true).build())
//                .addChild(Column.builder().key("roundingOrFine").title("抹零罚款").isSort(true).build())
                .addChild(Column.builder().key("grossProfitAmount").title("毛利").isSort(true).build())
                .addChild(Column.builder().key("grossProfitAmountRate").title("毛利率").isSort(true).build());
        table.addToFirstColumn(amount);

        if (MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode().equals(channelCodeId)
                || MetricCodeIdEnum.CHANNEL_DIRECT.getCode().equals(channelCodeId)) {
            //商超合同费用
            Column supermarket = Column.builder().key("supermarketGlobal").title("商超合同费用").build()
                    .addChild(Column.builder().key("marketDeducePoint").title("销售扣点").isSort(true).build())
//                    .addChild(Column.builder().key("fixedConsumablesCost").title("固定耗材费用").isSort(true).build())
//                    .addChild(Column.builder().key("guideManagementCost").title("导购管理费").isSort(true).build())
//                    .addChild(Column.builder().key("electricityCost").title("电费").isSort(true).build())
//                    .addChild(Column.builder().key("deliveryCost").title("配送费").isSort(true).build())
//                    .addChild(Column.builder().key("promotionCost").title("促销费").isSort(true).build())
//                    .addChild(Column.builder().key("marketingCost").title("推广费").isSort(true).build())
//                    .addChild(Column.builder().key("slottingFee").title("进场费").isSort(true).build())
//                    .addChild(Column.builder().key("otherCosts").title("其他费用").isSort(true).build())
                    .addChild(Column.builder().key("supermarketCostSubtotal").title("小计").isSort(true).build());
            table.addToFirstColumn(supermarket);

//            table.addToFirstColumn(Column.builder().key("employeeCost").title("员工社保").isSort(true).build());
        }

        if (MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode().equals(channelCodeId)) {
//            Column loss = Column.builder().key("lossGlobal").title("损耗").build()
//                    .addChild(Column.builder().key("clearanceLoss").title("出清损耗").isSort(true).build())
//                    .addChild(Column.builder().key("wastageLoss").title("损耗").isSort(true).build())
//                    .addChild(Column.builder().key("lossSubtotal").title("小计").isSort(true).build());
//            table.addToFirstColumn(loss);

//            Column marketSetup = Column.builder().key("marketSetupGlobal").title("超市基础配置费用").build()
//                    .addChild(Column.builder().key("freezerCost").title("冰柜折旧").isSort(true).build())
//                    .addChild(Column.builder().key("basicSuppliesCost").title("基础物料").isSort(true).build())
//                    .addChild(Column.builder().key("productSamplingCost").title("试吃物料").isSort(true).build())
//                    .addChild(Column.builder().key("personalBuyCost").title("自购物料").isSort(true).build())
//                    .addChild(Column.builder().key("supermarketSetupSubtotal").title("小计").isSort(true).build());
//            table.addToFirstColumn(marketSetup);

//            table.addToFirstColumn(Column.builder().key("campaignPromotionCost").title("活动推广费").isSort(true).build());
        }


        if (MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode().equals(channelCodeId)
                || MetricCodeIdEnum.CHANNEL_DIRECT.getCode().equals(channelCodeId)) {
            table.addToFirstColumn(Column.builder().key("totalSalesCost").title("销售费用合计").isSort(true).build());
        }

        Column fulfill = Column.builder().key("fulfill").title("履约费用").build()
                .addChild(Column.builder().key("directFulfillmentCost").title("直接费用").isSort(true).build())
                .addChild(Column.builder().key("allocatedFulfillmentCost").title("分摊费用").isSort(true).build())
                .addChild(Column.builder().key("fulfillmentCostsSubtotal").title("小计").isSort(true).build());
        table.addToFirstColumn(fulfill);

        table.addToFirstColumn(Column.builder().key("operatingGrossProfit").title("运营毛利").isSort(true).build());
        table.addToFirstColumn(Column.builder().key("operatingGrossProfitMargin").title("运营毛利率").isSort(true).build());
    }

    @Override
    public Map<String, BigDecimal> queryActualValue(Long tenantId, String metricCode, Collection<String> participateIds, TimeCondition timeCondition) {
        if (tenantId == null || timeCondition == null) {
            return Maps.newHashMap();
        }
        Map<String, BigDecimal> result = Maps.newHashMap();
        //查公司
        if (MetricCodeEnum.ALLCOMPANY.getCode().equals(metricCode)) {
            result.put(null, _queryAllCompany(tenantId, timeCondition));
            return result;
        }

        Set<String> ids = Sets.newHashSet(participateIds);
        Map<String, Set<String>> childrenMapping = Maps.newHashMap();
        //查部门-全子部门均查询
        if (MetricCodeEnum.DEPARTMENT.getCode().equals(metricCode)) {
            for (String departId : participateIds) {
                MultiResponse<Long> departResponse = departmentService.getSubDeptIdsByDeptId(tenantId, Long.valueOf(departId));
                if (departResponse != null && departResponse.getData() != null) {
                    departResponse.getData().stream().map(String::valueOf).forEach(subDeptId -> {
                        childrenMapping.computeIfAbsent(subDeptId, k -> new HashSet<>()).add(departId);
                        ids.add(subDeptId);
                    });
                }
            }
        }

        //对于不支持的 metricCode，设置默认值
        String conditionField = MetricCodeEnum.metricCodeMapping(metricCode);
        if (conditionField == null) {
            return result;
        }

        //查指标
        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (tableType == 1) { // 月度数据
            resultList = _queryStringFromMonth(tenantId, conditionField, ids, timeCondition);
        } else if (tableType == 2) { // 日度数据
            resultList = _queryStringFromDay(tenantId, conditionField, ids, timeCondition);
        } else {
            return result;
        }


        for (Map<String, Object> row : resultList) {
            if (row != null && row.containsKey(conditionField)) {
                String fieldValue = row.get(conditionField).toString();
                BigDecimal totalAmount = new BigDecimal(row.getOrDefault("totalAmount", "0").toString());
                if (childrenMapping.containsKey(fieldValue)) {
                    for (String parentId : childrenMapping.get(fieldValue)) {
                        BigDecimal existValue = result.getOrDefault(parentId, BigDecimal.ZERO);
                        existValue = existValue.add(totalAmount);
                        result.put(parentId, existValue);
                    }
                } else {
                    result.put(fieldValue, totalAmount);
                }
            }
        }
        return result;
    }

    @Override
    public IPage<Map<String, Object>> batchActualValue(DataFormQueryVO dataFormQueryVO) {
        // 表格查询
        // 验证和处理targetDataTypes参数
        validateAndProcessTargetDataTypes(dataFormQueryVO);

        // 表格查询 - 当前数据
        IPage<Map<String, Object>> tableResultMaps = batchCurrentActualValue(dataFormQueryVO);
        if (tableResultMaps == null || tableResultMaps.getRecords() == null) {
            return null;
        }
        List<Map<String, Object>> tableRecords = tableResultMaps.getRecords();

        // 计算总计数据用于占比计算
        Map<String, Object> totalData = batchCurrentTotalActualValue(dataFormQueryVO);

        // 计算环比数据
        Map<String, Map<String, Object>> momData = calculateMomData(dataFormQueryVO, tableRecords);

        // 计算同比数据
        Map<String, Map<String, Object>> yoyData = calculateYoyData(dataFormQueryVO, tableRecords);

        // 增强数据记录，添加占比和增长率
        enhanceRecordsWithCalculations(tableRecords, totalData, momData, yoyData, dataFormQueryVO.getQueryOptions().keySet());

        // 对需要应用层排序的计算字段进行排序
        sortCalculatedFields(tableRecords, dataFormQueryVO);

        return tableResultMaps;
    }

    @Override
    public Map<Long, BigDecimal> queryActualValueForUser(Long tenantId, Collection<Long> userIds, TimeCondition timeCondition) {
        if (tenantId == null || userIds == null || userIds.isEmpty() || timeCondition == null) {
            return Maps.newHashMap();
        }
        Map<Long, BigDecimal> result = Maps.newHashMap();

        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (tableType == 1) { // 月度数据
            resultList = _queryLongFromMonth(tenantId, "salesId", userIds, timeCondition);
        } else if (tableType == 2) { // 日度数据
            resultList = _queryLongFromDay(tenantId, "salesId", userIds, timeCondition);
        } else {
            return result;
        }

        for (Map<String, Object> row : resultList) {
            if (row != null && row.containsKey("salesId")) {
                result.put(Long.parseLong(row.get("salesId").toString()),
                        new BigDecimal(row.getOrDefault("totalAmount", "0").toString()));
            }
        }
        return result;
    }

    private List<Map<String, Object>> _queryLongFromMonth(Long tenantId, String field, Collection<Long> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryLongFromDay(Long tenantId, String field, Collection<Long> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryStringFromMonth(Long tenantId, String field, Collection<String> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryStringFromDay(Long tenantId, String field, Collection<String> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    // 全公司数据查询
    private BigDecimal _queryAllCompany(Long tenantId, TimeCondition timeCondition) {
        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (1 == tableType) {
            QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                    .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                    .eq("tenantId", tenantId)
                    .eq("isDeleted", 0)
                    .select("SUM(salesAmount) AS totalAmount");
            resultList = st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
        } else if (2 == tableType) {
            QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                    .eq("produceId", DataConstants.SUMMARY_GOODS_ID) // 取合计计算
                    .eq("tenantId", tenantId)
                    .eq("isDeleted", 0)
                    .select("SUM(salesAmount) AS totalAmount");
            resultList = st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
        } else {
            return BigDecimal.ZERO;
        }
        if (!resultList.isEmpty() && resultList.get(0) != null) {
            return new BigDecimal(resultList.get(0).getOrDefault("totalAmount", "0").toString());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 增强版的批量实际值查询，支持毛利额和毛利率计算
     */
    private IPage<Map<String, Object>> _batchActualValue(DataFormQueryVO dataFormQueryVO, int queryType) {
        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        List<String> selectDataColumns = TargetDataTypeEnum.listByQueryType(dataFormQueryVO.getTargetDataTypes(), queryType);
        if (selectDataColumns.isEmpty()) return null;

        IPage<Map<String, Object>> tableResultMaps;
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, selectDataColumns, queryType);
            tableResultMaps = st_trd_customer_produce_day_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, selectDataColumns, queryType);
            tableResultMaps = st_trd_customer_produce_month_infoRepository.pageMaps(
                    Page.of(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize()), wrapper);
        }

        return tableResultMaps;
    }

    /**
     * 查询当前数据
     */
    private IPage<Map<String, Object>> batchCurrentActualValue(DataFormQueryVO dataFormQueryVO) {
        return _batchActualValue(dataFormQueryVO, 0);
    }

    /**
     * 查询当前合计数据
     */
    private Map<String, Object> batchCurrentTotalActualValue(DataFormQueryVO dataFormQueryVO) {
        IPage<Map<String, Object>> result = _batchActualValue(dataFormQueryVO, 1);
        return Optional.ofNullable(result).map(IPage::getRecords).map(list -> list.get(0)).orElse(Maps.newHashMap());
    }

    /**
     * 计算环比数据
     */
    private Map<String, Map<String, Object>> calculateMomData(DataFormQueryVO dataFormQueryVO, List<Map<String, Object>> tableRecords) {
        // 创建环比时间条件
        TimeCondition momTimeCondition = dataFormQueryVO.getTimeCondition().copy().initMom();
        return _calculatePreData(dataFormQueryVO, momTimeCondition, tableRecords, 2);
    }

    /**
     * 计算同比数据
     */
    private Map<String, Map<String, Object>> calculateYoyData(DataFormQueryVO dataFormQueryVO, List<Map<String, Object>> tableRecords) {
        // 创建同比时间条件
        TimeCondition yoyTimeCondition = dataFormQueryVO.getTimeCondition().copy().initYoy();
        return _calculatePreData(dataFormQueryVO, yoyTimeCondition, tableRecords, 3);
    }

    private Map<String, Map<String, Object>> _calculatePreData(DataFormQueryVO dataFormQueryVO, TimeCondition timeCondition,
                                                               List<Map<String, Object>> tableRecords, int queryType) {
        // 当前数据的条件拿出来，当查询条件
        Map<String, List<String>> queryOptions = dataFormQueryVO.getQueryOptions();
        tableRecords.forEach(record -> {
            for (String metricCode : queryOptions.keySet()) {
                String tableColumnName = MetricCodeEnum.metricCodeMapping(metricCode);
                if (record.containsKey(tableColumnName)) {
                    queryOptions.computeIfAbsent(metricCode, r0 -> Lists.newArrayList()).add(record.get(tableColumnName).toString());
                }
            }
        });

        DataFormQueryVO preQueryVO = new DataFormQueryVO();
        preQueryVO.setQueryOptions(queryOptions);
        preQueryVO.setTimeCondition(timeCondition);
        preQueryVO.setTenantId(dataFormQueryVO.getTenantId());
        preQueryVO.setPageIndex(1);
        preQueryVO.setPageSize(Integer.MAX_VALUE); // 获取所有数据
        preQueryVO.setTargetDataTypes(dataFormQueryVO.getTargetDataTypes());

        IPage<Map<String, Object>> preResults = _batchActualValue(preQueryVO, queryType);

        // 转换为Map，以便快速查找
        Map<String, Map<String, Object>> resultMap = Maps.newHashMap();
        if (preResults != null && preResults.getRecords() != null) {
            for (Map<String, Object> record : preResults.getRecords()) {
                String key = buildRecordKey(record, dataFormQueryVO.getQueryOptions().keySet());
                resultMap.put(key, record);
            }
        }

        return resultMap;
    }


    /**
     * 构建记录的唯一键，用于匹配环比和同比数据
     */
    private String buildRecordKey(Map<String, Object> record, Set<String> queryOptions) {
        StringBuilder keyBuilder = new StringBuilder();
        for (String option : queryOptions) {
            String column = MetricCodeEnum.metricCodeMapping(option);
            if (column != null && record.containsKey(column)) {
                keyBuilder.append(column).append(":").append(record.get(column)).append("|");
            }
        }
        return keyBuilder.toString();
    }

    /**
     * 增强记录数据，添加占比和增长率计算
     */
    private void enhanceRecordsWithCalculations(List<Map<String, Object>> records,
                                                Map<String, Object> totalData,
                                                Map<String, Map<String, Object>> momData,
                                                Map<String, Map<String, Object>> yoyData,
                                                Set<String> queryOptions) {
        BigDecimal totalSalesAmount = getBigDecimalValue(totalData, "salesAmount");
        BigDecimal totalGrossProfitAmount = getBigDecimalValue(totalData, "grossProfitAmount");

        for (Map<String, Object> record : records) {
            String recordKey = buildRecordKey(record, queryOptions);

            // 当前数据
            BigDecimal salesAmount = getBigDecimalValue(record, "salesAmount");
            BigDecimal grossProfitAmount = getBigDecimalValue(record, "grossProfitAmount");
            BigDecimal grossProfitMargin = getBigDecimalValue(record, "grossProfitMargin");

            // 计算占比
            record.put("salesAmountRatio", calculateRatio(salesAmount, totalSalesAmount));
            record.put("grossProfitAmountRatio", calculateRatio(grossProfitAmount, totalGrossProfitAmount));

            // 计算环比增长
            Map<String, Object> momRecord = momData.get(recordKey);
            if (momRecord != null) {
                record.put("salesAmountMomGrowth", calculateGrowthRate(salesAmount, getBigDecimalValue(momRecord, "salesAmount")));
                record.put("grossProfitAmountMomGrowth", calculateGrowthRate(grossProfitAmount, getBigDecimalValue(momRecord, "grossProfitAmount")));
                record.put("grossProfitMarginMomGrowth", calculateGrowthRate(grossProfitMargin, getBigDecimalValue(momRecord, "grossProfitMargin")));
            } else {
                record.put("salesAmountMomGrowth", BigDecimal.ZERO);
                record.put("grossProfitAmountMomGrowth", BigDecimal.ZERO);
                record.put("grossProfitMarginMomGrowth", BigDecimal.ZERO);
            }

            // 计算同比增长
            Map<String, Object> yoyRecord = yoyData.get(recordKey);
            if (yoyRecord != null) {
                record.put("salesAmountYoyGrowth", calculateGrowthRate(salesAmount, getBigDecimalValue(yoyRecord, "salesAmount")));
                record.put("grossProfitAmountYoyGrowth", calculateGrowthRate(grossProfitAmount, getBigDecimalValue(yoyRecord, "grossProfitAmount")));
                record.put("grossProfitMarginYoyGrowth", calculateGrowthRate(grossProfitMargin, getBigDecimalValue(yoyRecord, "grossProfitMargin")));
            } else {
                record.put("salesAmountYoyGrowth", BigDecimal.ZERO);
                record.put("grossProfitAmountYoyGrowth", BigDecimal.ZERO);
                record.put("grossProfitMarginYoyGrowth", BigDecimal.ZERO);
            }
        }
    }

    /**
     * 添加增强的表格列
     * 根据targetDataTypes参数决定要添加的列
     *
     * @param table           表格对象
     * @param targetDataTypes 目标数据类型列表，如果为空则添加所有列
     * @return 数据列名称列表
     */
    private List<String> addEnhancedColumns(Table table, List<String> targetDataTypes) {
        List<String> dataColumns = Lists.newArrayList();

        // 如果没有指定targetDataTypes，则默认显示所有数据类型
        boolean showAll = targetDataTypes == null || targetDataTypes.isEmpty();

        // 销售额相关列
        if (showAll || targetDataTypes.contains("salesAmount")) {
            table.addToFirstColumn(Column.builder().key("salesAmount").title("销售额").isSort(true).build());
            dataColumns.add("salesAmount");
        }
        if (showAll || targetDataTypes.contains("salesAmountRatio")) {
            table.addToFirstColumn(Column.builder().key("salesAmountRatio").title("销售额占比").isSort(true).build());
            dataColumns.add("salesAmountRatio");
        }
        if (showAll || targetDataTypes.contains("salesAmountMomGrowth")) {
            table.addToFirstColumn(Column.builder().key("salesAmountMomGrowth").title("销售额环比增长").isSort(true).build());
            dataColumns.add("salesAmountMomGrowth");
        }
        if (showAll || targetDataTypes.contains("salesAmountYoyGrowth")) {
            table.addToFirstColumn(Column.builder().key("salesAmountYoyGrowth").title("销售额同比增长").isSort(true).build());
            dataColumns.add("salesAmountYoyGrowth");
        }

        // 毛利额相关列
        if (showAll || targetDataTypes.contains("grossProfitAmount")) {
            table.addToFirstColumn(Column.builder().key("grossProfitAmount").title("毛利额").isSort(true).build());
            dataColumns.add("grossProfitAmount");
        }
        if (showAll || targetDataTypes.contains("grossProfitAmountRatio")) {
            table.addToFirstColumn(Column.builder().key("grossProfitAmountRatio").title("毛利额占比").isSort(true).build());
            dataColumns.add("grossProfitAmountRatio");
        }
        if (showAll || targetDataTypes.contains("grossProfitAmountMomGrowth")) {
            table.addToFirstColumn(Column.builder().key("grossProfitAmountMomGrowth").title("毛利额环比增长").isSort(true).build());
            dataColumns.add("grossProfitAmountMomGrowth");
        }
        if (showAll || targetDataTypes.contains("grossProfitAmountYoyGrowth")) {
            table.addToFirstColumn(Column.builder().key("grossProfitAmountYoyGrowth").title("毛利额同比增长").isSort(true).build());
            dataColumns.add("grossProfitAmountYoyGrowth");
        }

        // 毛利率相关列
        if (showAll || targetDataTypes.contains("grossProfitMargin")) {
            table.addToFirstColumn(Column.builder().key("grossProfitMargin").title("毛利率").isSort(true).build());
            dataColumns.add("grossProfitMargin");
        }
        if (showAll || targetDataTypes.contains("grossProfitMarginMomGrowth")) {
            table.addToFirstColumn(Column.builder().key("grossProfitMarginMomGrowth").title("毛利率环比增长").isSort(true).build());
            dataColumns.add("grossProfitMarginMomGrowth");
        }
        if (showAll || targetDataTypes.contains("grossProfitMarginYoyGrowth")) {
            table.addToFirstColumn(Column.builder().key("grossProfitMarginYoyGrowth").title("毛利率同比增长").isSort(true).build());
            dataColumns.add("grossProfitMarginYoyGrowth");
        }

        return dataColumns;
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        if (map == null) {
            return BigDecimal.ZERO;
        }
        Object value = map.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算占比（百分比）
     */
    private BigDecimal calculateRatio(BigDecimal value, BigDecimal total) {
        if (total == null || total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return value.divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 计算增长率（百分比）
     */
    private BigDecimal calculateGrowthRate(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return current.subtract(previous).divide(previous, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 验证和处理targetDataTypes参数
     *
     * @param dataFormQueryVO 查询参数对象
     */
    private void validateAndProcessTargetDataTypes(DataFormQueryVO dataFormQueryVO) {
        List<String> targetDataTypes = dataFormQueryVO.getTargetDataTypes();

        // 如果没有指定targetDataTypes，则不需要验证
        if (targetDataTypes == null || targetDataTypes.isEmpty()) {
            throw new BizCustomException(400, "必须选择需要查询的指标 ");
        }

        // 验证每个targetDataType是否有效
        for (String targetDataType : targetDataTypes) {
            if (!TargetDataTypeEnum.isValidCode(targetDataType)) {
                throw new BizCustomException(400, "无效的targetDataType: " + targetDataType);
            }

        }
    }

    /**
     * 处理计算字段的排序
     *
     * @param wrapper           查询包装器
     * @param dataFormQueryVO   查询参数对象
     * @param selectColumns     选择的列
     * @param selectDataColumns 数据列
     */
    private <T> void handleSortingForCalculatedFields(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO,
                                                      List<String> selectColumns, List<String> selectDataColumns) {
        String sortBy = dataFormQueryVO.getSortBy();
        String sort = dataFormQueryVO.getSort();

        // 如果没有指定排序字段，使用默认排序
        if (StringUtils.isBlank(sortBy)) {
            wrapper.orderBy(true, true, selectColumns);
            return;
        }

        // 定义所有计算字段（占比和增长率字段）
        Set<String> calculatedFields = Sets.newHashSet(
                "salesAmountRatio", "salesAmountMomGrowth", "salesAmountYoyGrowth",
                "grossProfitAmountRatio", "grossProfitAmountMomGrowth", "grossProfitAmountYoyGrowth",
                "grossProfitMarginMomGrowth", "grossProfitMarginYoyGrowth"
        );

        // 确定排序方向
        boolean isAsc = !"desc".equals(sort);

        if (calculatedFields.contains(sortBy)) {
            // 对于计算字段，我们需要在SQL中构建相应的计算表达式进行排序
            String orderByExpression = buildCalculatedFieldOrderByExpression(sortBy, dataFormQueryVO);
            if (StringUtils.isNotBlank(orderByExpression)) {
                wrapper.orderBy(true, isAsc, orderByExpression);
            } else {
                // 如果无法构建计算表达式，则使用默认排序
                wrapper.orderBy(true, true, selectColumns);
            }
        } else {
            // 对于普通字段，直接排序
            wrapper.orderBy(true, isAsc, sortBy);
        }
    }

    /**
     * 构建计算字段的排序表达式
     *
     * @param sortBy          排序字段
     * @param dataFormQueryVO 查询参数
     * @return SQL排序表达式
     */
    private String buildCalculatedFieldOrderByExpression(String sortBy, DataFormQueryVO dataFormQueryVO) {
        switch (sortBy) {
            case "salesAmountRatio":
                // 销售额占比 = 当前销售额 / 总销售额 * 100
                return "CASE WHEN (SELECT SUM(salesAmount) FROM " + getTableName(dataFormQueryVO) +
                        " WHERE tenantId = " + dataFormQueryVO.getTenantId() +
                        " AND visit_date BETWEEN '" + dataFormQueryVO.getTimeCondition().getStartDate() +
                        "' AND '" + dataFormQueryVO.getTimeCondition().getEndDate() +
                        "' AND isDeleted = 0 AND produceId = 0) > 0 " +
                        "THEN SUM(salesAmount) / (SELECT SUM(salesAmount) FROM " + getTableName(dataFormQueryVO) +
                        " WHERE tenantId = " + dataFormQueryVO.getTenantId() +
                        " AND visit_date BETWEEN '" + dataFormQueryVO.getTimeCondition().getStartDate() +
                        "' AND '" + dataFormQueryVO.getTimeCondition().getEndDate() +
                        "' AND isDeleted = 0 AND produceId = 0) * 100 ELSE 0 END";

            case "grossProfitAmountRatio":
                // 毛利额占比 = 当前毛利额 / 总毛利额 * 100
                return "CASE WHEN (SELECT SUM(salesAmount - costAmount) FROM " + getTableName(dataFormQueryVO) +
                        " WHERE tenantId = " + dataFormQueryVO.getTenantId() +
                        " AND visit_date BETWEEN '" + dataFormQueryVO.getTimeCondition().getStartDate() +
                        "' AND '" + dataFormQueryVO.getTimeCondition().getEndDate() +
                        "' AND isDeleted = 0 AND produceId = 0) > 0 " +
                        "THEN SUM(salesAmount - costAmount) / (SELECT SUM(salesAmount - costAmount) FROM " + getTableName(dataFormQueryVO) +
                        " WHERE tenantId = " + dataFormQueryVO.getTenantId() +
                        " AND visit_date BETWEEN '" + dataFormQueryVO.getTimeCondition().getStartDate() +
                        "' AND '" + dataFormQueryVO.getTimeCondition().getEndDate() +
                        "' AND isDeleted = 0 AND produceId = 0) * 100 ELSE 0 END";

            // 对于环比和同比增长，由于需要复杂的子查询，暂时返回null，让应用层处理
            case "salesAmountMomGrowth":
            case "salesAmountYoyGrowth":
            case "grossProfitAmountMomGrowth":
            case "grossProfitAmountYoyGrowth":
            case "grossProfitMarginMomGrowth":
            case "grossProfitMarginYoyGrowth":
                // 这些字段需要复杂的时间计算，暂时在应用层排序
                return null;

            default:
                return null;
        }
    }

    /**
     * 获取表名
     *
     * @param dataFormQueryVO 查询参数
     * @return 表名
     */
    private String getTableName(DataFormQueryVO dataFormQueryVO) {
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            return "st_trd_customer_produce_day_info";
        } else {
            return "st_trd_customer_produce_month_info";
        }
    }

    /**
     * 对计算字段进行排序（应用层排序，用于复杂计算字段）
     *
     * @param records         数据记录列表
     * @param dataFormQueryVO 查询参数对象
     */
    private void sortCalculatedFields(List<Map<String, Object>> records, DataFormQueryVO dataFormQueryVO) {
        String sortBy = dataFormQueryVO.getSortBy();
        String sort = dataFormQueryVO.getSort();

        // 如果没有指定排序字段，则不进行排序
        if (StringUtils.isBlank(sortBy) || records == null || records.isEmpty()) {
            return;
        }

        // 定义需要应用层排序的计算字段（主要是环比和同比增长）
        Set<String> appLayerSortFields = Sets.newHashSet(
                "salesAmountMomGrowth", "salesAmountYoyGrowth",
                "grossProfitAmountMomGrowth", "grossProfitAmountYoyGrowth",
                "grossProfitMarginMomGrowth", "grossProfitMarginYoyGrowth"
        );

        // 只对需要应用层排序的计算字段进行排序
        if (!appLayerSortFields.contains(sortBy)) {
            return;
        }

        // 确定排序方向，默认为降序
        boolean isAsc = "asc".equalsIgnoreCase(sort);

        // 对记录进行排序
        records.sort((record1, record2) -> {
            BigDecimal value1 = getBigDecimalValue(record1, sortBy);
            BigDecimal value2 = getBigDecimalValue(record2, sortBy);

            int compareResult = value1.compareTo(value2);

            // 如果是升序，直接返回比较结果；如果是降序，返回相反的结果
            return isAsc ? compareResult : -compareResult;
        });
    }


}
